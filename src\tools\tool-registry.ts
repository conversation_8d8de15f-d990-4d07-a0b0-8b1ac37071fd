import { EventEmitter } from 'events';
import { <PERSON><PERSON> } from 'winston';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, AgentContext, ToolError } from '@/types';

// Import tool implementations
import { ShellTool } from './shell/shell-tool';
import { FileSystemTool } from './filesystem/filesystem-tool';
import { SearchTool } from './filesystem/search-tool';
import { BackupTool } from './filesystem/backup-tool';
import { ProjectAnalysisTool } from './analysis/project-analysis-tool';
import { CodeAnalysisTool } from './analysis/code-analysis-tool';
import { ParallelExecutor } from './parallel/parallel-executor';

export class ToolRegistry extends EventEmitter {
  private tools: Map<string, Tool> = new Map();
  private readonly logger: Logger;
  private readonly parallelExecutor: ParallelExecutor;
  private dangerousToolsEnabled = false;

  constructor(logger: Logger) {
    super();
    this.logger = logger;
    this.parallelExecutor = new ParallelExecutor(logger);
    this.initializeTools();
  }

  private initializeTools(): void {
    const tools: Tool[] = [
      new ShellTool(this.logger),
      new FileSystemTool(this.logger),
      new SearchTool(this.logger),
      new BackupTool(this.logger),
      new ProjectAnalysisTool(this.logger),
      new CodeAnalysisTool(this.logger),
    ];

    for (const tool of tools) {
      this.registerTool(tool);
    }

    this.logger.info(`Initialized ${tools.length} tools`);
  }

  registerTool(tool: Tool): void {
    if (this.tools.has(tool.name)) {
      throw new ToolError(`Tool already registered: ${tool.name}`, tool.name);
    }

    this.tools.set(tool.name, tool);
    this.logger.debug(`Registered tool: ${tool.name}`);
    this.emit('toolRegistered', { tool });
  }

  unregisterTool(name: string): void {
    if (!this.tools.has(name)) {
      throw new ToolError(`Tool not found: ${name}`, name);
    }

    this.tools.delete(name);
    this.logger.debug(`Unregistered tool: ${name}`);
    this.emit('toolUnregistered', { name });
  }

  getTool(name: string): Tool | undefined {
    return this.tools.get(name);
  }

  getAvailableTools(): Tool[] {
    return Array.from(this.tools.values()).filter(tool => {
      if (tool.dangerous && !this.dangerousToolsEnabled) {
        return false;
      }
      return true;
    });
  }

  async executeTool(
    name: string, 
    parameters: unknown, 
    context: AgentContext
  ): Promise<ToolResult> {
    const tool = this.getTool(name);
    if (!tool) {
      throw new ToolError(`Tool not found: ${name}`, name);
    }

    if (tool.dangerous && !this.dangerousToolsEnabled) {
      throw new ToolError(`Dangerous tool not enabled: ${name}`, name);
    }

    this.logger.info(`Executing tool: ${name}`, { parameters });
    this.emit('toolExecutionStarted', { name, parameters });

    const startTime = Date.now();
    
    try {
      // Validate parameters
      const validatedParams = tool.parameters.parse(parameters);
      
      // Execute tool
      const result = await tool.execute(validatedParams, context);
      
      const duration = Date.now() - startTime;
      this.logger.info(`Tool execution completed: ${name}`, { 
        duration, 
        success: result.success 
      });
      
      this.emit('toolExecutionCompleted', { 
        name, 
        parameters: validatedParams, 
        result, 
        duration 
      });

      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.logger.error(`Tool execution failed: ${name}`, { 
        error, 
        duration, 
        parameters 
      });
      
      this.emit('toolExecutionFailed', { 
        name, 
        parameters, 
        error, 
        duration 
      });

      if (error instanceof ToolError) {
        throw error;
      }

      throw new ToolError(
        `Tool execution failed: ${(error as Error).message}`,
        name,
        { originalError: error, parameters }
      );
    }
  }

  async executeToolsParallel(
    executions: Array<{
      name: string;
      parameters: unknown;
    }>,
    context: AgentContext,
    maxConcurrency = 5
  ): Promise<ToolResult[]> {
    this.logger.info(`Executing ${executions.length} tools in parallel`, { maxConcurrency });

    // Filter out tools that don't support parallel execution
    const parallelExecutions = executions.filter(exec => {
      const tool = this.getTool(exec.name);
      return tool?.parallel ?? false;
    });

    const sequentialExecutions = executions.filter(exec => {
      const tool = this.getTool(exec.name);
      return !(tool?.parallel ?? false);
    });

    if (sequentialExecutions.length > 0) {
      this.logger.warn(`${sequentialExecutions.length} tools don't support parallel execution`);
    }

    // Execute parallel tools
    const parallelResults = await this.parallelExecutor.executeParallel(
      parallelExecutions.map(exec => () => this.executeTool(exec.name, exec.parameters, context)),
      maxConcurrency
    );

    // Execute sequential tools
    const sequentialResults: ToolResult[] = [];
    for (const exec of sequentialExecutions) {
      try {
        const result = await this.executeTool(exec.name, exec.parameters, context);
        sequentialResults.push(result);
      } catch (error) {
        sequentialResults.push({
          success: false,
          error: (error as Error).message,
        });
      }
    }

    return [...parallelResults, ...sequentialResults];
  }

  async executeToolChain(
    chain: Array<{
      name: string;
      parameters: unknown;
      condition?: (previousResults: ToolResult[]) => boolean;
    }>,
    context: AgentContext
  ): Promise<ToolResult[]> {
    this.logger.info(`Executing tool chain with ${chain.length} steps`);

    const results: ToolResult[] = [];

    for (let i = 0; i < chain.length; i++) {
      const step = chain[i]!;
      
      // Check condition if provided
      if (step.condition && !step.condition(results)) {
        this.logger.info(`Skipping tool ${step.name} due to condition`);
        continue;
      }

      try {
        const result = await this.executeTool(step.name, step.parameters, context);
        results.push(result);

        // Stop chain if tool failed and no error handling
        if (!result.success) {
          this.logger.warn(`Tool chain stopped at step ${i + 1} due to failure`);
          break;
        }
      } catch (error) {
        this.logger.error(`Tool chain failed at step ${i + 1}`, { error });
        results.push({
          success: false,
          error: (error as Error).message,
        });
        break;
      }
    }

    return results;
  }

  getToolsByCategory(category: string): Tool[] {
    return this.getAvailableTools().filter(tool => 
      tool.name.startsWith(category) || tool.description.includes(category)
    );
  }

  searchTools(query: string): Tool[] {
    const lowerQuery = query.toLowerCase();
    return this.getAvailableTools().filter(tool =>
      tool.name.toLowerCase().includes(lowerQuery) ||
      tool.description.toLowerCase().includes(lowerQuery)
    );
  }

  setDangerousToolsEnabled(enabled: boolean): void {
    this.dangerousToolsEnabled = enabled;
    this.logger.info(`Dangerous tools ${enabled ? 'enabled' : 'disabled'}`);
  }

  isDangerousToolsEnabled(): boolean {
    return this.dangerousToolsEnabled;
  }

  getToolInfo(name: string): {
    name: string;
    description: string;
    parameters: object;
    parallel: boolean;
    dangerous: boolean;
  } | undefined {
    const tool = this.getTool(name);
    if (!tool) return undefined;

    return {
      name: tool.name,
      description: tool.description,
      parameters: (tool.parameters as any)._def?.shape || {},
      parallel: tool.parallel,
      dangerous: tool.dangerous,
    };
  }

  getAllToolsInfo(): Array<{
    name: string;
    description: string;
    parameters: object;
    parallel: boolean;
    dangerous: boolean;
  }> {
    return this.getAvailableTools().map(tool => ({
      name: tool.name,
      description: tool.description,
      parameters: (tool.parameters as any)._def?.shape || {},
      parallel: tool.parallel,
      dangerous: tool.dangerous,
    }));
  }

  async validateToolParameters(name: string, parameters: unknown): Promise<boolean> {
    const tool = this.getTool(name);
    if (!tool) return false;

    try {
      tool.parameters.parse(parameters);
      return true;
    } catch {
      return false;
    }
  }

  async shutdown(): Promise<void> {
    await this.parallelExecutor.shutdown();
    this.tools.clear();
    this.logger.info('Tool registry shutdown');
  }
}
