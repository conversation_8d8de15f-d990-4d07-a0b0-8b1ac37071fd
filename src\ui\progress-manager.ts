import { EventEmitter } from 'events';
import { Logger } from 'winston';
import { Listr, ListrTask } from 'listr2';
import ora, { Ora } from 'ora';
import chalk from 'chalk';

export interface ProgressTask {
  id: string;
  title: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped';
  progress?: number;
  message?: string;
  error?: string;
  startTime?: Date;
  endTime?: Date;
  subtasks?: ProgressTask[];
}

export interface ProgressOptions {
  concurrent?: boolean;
  exitOnError?: boolean;
  showSubtasks?: boolean;
  renderer?: 'default' | 'simple' | 'verbose' | 'silent';
}

export class ProgressManager extends EventEmitter {
  private readonly logger: Logger;
  private activeTasks = new Map<string, ProgressTask>();
  private activeSpinners = new Map<string, Ora>();
  private listrInstance?: Listr;

  constructor(logger: Logger) {
    super();
    this.logger = logger;
  }

  createTaskList(tasks: ProgressTask[], options: ProgressOptions = {}): Listr {
    const listrTasks: ListrTask[] = tasks.map(task => ({
      title: task.title,
      task: async (_ctx, taskInstance) => {
        this.activeTasks.set(task.id, { ...task, status: 'running', startTime: new Date() });
        this.emit('taskStarted', task);

        try {
          // Simulate task execution - in real implementation, this would be the actual task
          await this.executeTask(task, taskInstance);
          
          const completedTask = { ...task, status: 'completed' as const, endTime: new Date() };
          this.activeTasks.set(task.id, completedTask);
          this.emit('taskCompleted', completedTask);
        } catch (error) {
          const failedTask = { 
            ...task, 
            status: 'failed' as const, 
            error: (error as Error).message,
            endTime: new Date()
          };
          this.activeTasks.set(task.id, failedTask);
          this.emit('taskFailed', failedTask);
          throw error;
        }
      },
      options: {
        persistentOutput: true,
      },
    }));

    this.listrInstance = new Listr(listrTasks, {
      concurrent: options.concurrent || false,
      exitOnError: options.exitOnError !== false,
      renderer: options.renderer || 'default',
      rendererOptions: {
        showSubtasks: options.showSubtasks !== false,
        collapse: false,
        showErrorMessage: true,
      },
    });

    return this.listrInstance;
  }

  async runTaskList(tasks: ProgressTask[], options: ProgressOptions = {}): Promise<void> {
    const taskList = this.createTaskList(tasks, options);
    
    try {
      await taskList.run();
      this.logger.info('All tasks completed successfully');
    } catch (error) {
      this.logger.error('Task list execution failed', { error });
      throw error;
    }
  }

  private async executeTask(task: ProgressTask, taskInstance: any): Promise<void> {
    // This is where the actual task execution would happen
    // For now, we'll simulate progress updates
    
    if (task.subtasks && task.subtasks.length > 0) {
      // Handle subtasks
      for (let i = 0; i < task.subtasks.length; i++) {
        const subtask = task.subtasks[i]!;
        taskInstance.output = `Executing: ${subtask.title}`;
        
        // Simulate subtask execution
        await this.simulateProgress(subtask, (progress) => {
          const percentage = Math.round((i + progress) / task.subtasks!.length * 100);
          taskInstance.title = `${task.title} (${percentage}%)`;
        });
      }
    } else {
      // Handle single task
      await this.simulateProgress(task, (progress) => {
        const percentage = Math.round(progress * 100);
        taskInstance.title = `${task.title} (${percentage}%)`;
      });
    }
  }

  private async simulateProgress(
    _task: ProgressTask,
    onProgress: (progress: number) => void
  ): Promise<void> {
    // Simulate task execution with progress updates
    const steps = 10;
    for (let i = 0; i <= steps; i++) {
      await new Promise(resolve => setTimeout(resolve, 100)); // Simulate work
      onProgress(i / steps);
    }
  }

  startSpinner(id: string, message: string): Ora {
    const spinner = ora(message).start();
    this.activeSpinners.set(id, spinner);
    
    this.logger.debug('Spinner started', { id, message });
    this.emit('spinnerStarted', { id, message });
    
    return spinner;
  }

  updateSpinner(id: string, message: string): void {
    const spinner = this.activeSpinners.get(id);
    if (spinner) {
      spinner.text = message;
      this.logger.debug('Spinner updated', { id, message });
    }
  }

  succeedSpinner(id: string, message?: string): void {
    const spinner = this.activeSpinners.get(id);
    if (spinner) {
      spinner.succeed(message);
      this.activeSpinners.delete(id);
      this.logger.debug('Spinner succeeded', { id, message });
      this.emit('spinnerSucceeded', { id, message });
    }
  }

  failSpinner(id: string, message?: string): void {
    const spinner = this.activeSpinners.get(id);
    if (spinner) {
      spinner.fail(message);
      this.activeSpinners.delete(id);
      this.logger.debug('Spinner failed', { id, message });
      this.emit('spinnerFailed', { id, message });
    }
  }

  stopSpinner(id: string): void {
    const spinner = this.activeSpinners.get(id);
    if (spinner) {
      spinner.stop();
      this.activeSpinners.delete(id);
      this.logger.debug('Spinner stopped', { id });
    }
  }

  showProgressBar(
    current: number, 
    total: number, 
    label: string, 
    width = 40
  ): void {
    const percentage = Math.round((current / total) * 100);
    const filledWidth = Math.round((width * current) / total);
    const emptyWidth = width - filledWidth;
    
    const filledBar = '█'.repeat(filledWidth);
    const emptyBar = '░'.repeat(emptyWidth);
    const progressBar = filledBar + emptyBar;
    
    const progressText = `${label} [${chalk.cyan(progressBar)}] ${percentage}% (${current}/${total})`;
    
    // Clear current line and show progress
    process.stdout.write('\r\x1b[K');
    process.stdout.write(progressText);
    
    if (current === total) {
      console.log(); // New line when complete
    }
  }

  createMultiProgress(): MultiProgressManager {
    return new MultiProgressManager(this.logger);
  }

  getActiveTaskCount(): number {
    return Array.from(this.activeTasks.values()).filter(
      task => task.status === 'running'
    ).length;
  }

  getTaskStatus(id: string): ProgressTask | undefined {
    return this.activeTasks.get(id);
  }

  getAllTasks(): ProgressTask[] {
    return Array.from(this.activeTasks.values());
  }

  clearCompletedTasks(): void {
    for (const [id, task] of this.activeTasks.entries()) {
      if (task.status === 'completed' || task.status === 'failed') {
        this.activeTasks.delete(id);
      }
    }
  }

  stopAllSpinners(): void {
    for (const [id, spinner] of this.activeSpinners.entries()) {
      spinner.stop();
      this.activeSpinners.delete(id);
    }
  }

  async shutdown(): Promise<void> {
    this.stopAllSpinners();
    this.activeTasks.clear();
    this.removeAllListeners();
    this.logger.info('Progress manager shutdown');
  }
}

export class MultiProgressManager {
  private readonly logger: Logger;
  private progressBars = new Map<string, {
    current: number;
    total: number;
    label: string;
    lastUpdate: number;
  }>();

  constructor(logger: Logger) {
    this.logger = logger;
  }

  addProgress(id: string, label: string, total: number): void {
    this.progressBars.set(id, {
      current: 0,
      total,
      label,
      lastUpdate: Date.now(),
    });
    this.render();
  }

  updateProgress(id: string, current: number): void {
    const progress = this.progressBars.get(id);
    if (progress) {
      progress.current = current;
      progress.lastUpdate = Date.now();
      this.render();
    }
  }

  removeProgress(id: string): void {
    this.progressBars.delete(id);
    this.render();
  }

  private render(): void {
    // Clear previous output
    const lineCount = this.progressBars.size + 1;
    for (let i = 0; i < lineCount; i++) {
      process.stdout.write('\x1b[1A\x1b[K'); // Move up and clear line
    }

    // Render all progress bars
    console.log(chalk.bold('Progress Status:'));
    for (const [_id, progress] of this.progressBars.entries()) {
      const percentage = Math.round((progress.current / progress.total) * 100);
      const barWidth = 30;
      const filledWidth = Math.round((barWidth * progress.current) / progress.total);
      const bar = '█'.repeat(filledWidth) + '░'.repeat(barWidth - filledWidth);
      
      console.log(
        `${progress.label}: [${chalk.cyan(bar)}] ${percentage}% (${progress.current}/${progress.total})`
      );
    }
  }

  clear(): void {
    this.progressBars.clear();
    console.log('\x1b[2J\x1b[H'); // Clear screen and move to top
  }
}

// Utility function to create a simple progress task
export function createProgressTask(
  id: string,
  title: string,
  subtasks?: Array<{ id: string; title: string }>
): ProgressTask {
  const result: ProgressTask = {
    id,
    title,
    status: 'pending',
  };

  if (subtasks) {
    result.subtasks = subtasks.map(sub => ({
      id: sub.id,
      title: sub.title,
      status: 'pending',
    }));
  }

  return result;
}

// Utility function to estimate task duration
export function estimateTaskDuration(task: ProgressTask): number {
  // Simple estimation based on task complexity
  let baseTime = 1000; // 1 second base
  
  if (task.subtasks) {
    baseTime += task.subtasks.length * 500; // 500ms per subtask
  }
  
  // Add complexity factors based on task title keywords
  const complexityKeywords = ['analyze', 'build', 'compile', 'install', 'download'];
  const hasComplexity = complexityKeywords.some(keyword => 
    task.title.toLowerCase().includes(keyword)
  );
  
  if (hasComplexity) {
    baseTime *= 2;
  }
  
  return baseTime;
}
